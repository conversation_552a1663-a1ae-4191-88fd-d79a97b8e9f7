#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Russian Text Extraction and Cleaning for TTS
Extracts Russian text from PDF and cleans it for Text-to-Speech processing
"""

import re
import sys
from pathlib import Path

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF using multiple fallback methods"""
    text = ""
    
    # Try pdfplumber first (better for complex layouts)
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        print(f"Successfully extracted text using pdfplumber from {pdf_path}")
        return text
    except ImportError:
        print("pdfplumber not available, trying PyPDF2...")
    except Exception as e:
        print(f"Error with pdfplumber: {e}, trying PyPDF2...")
    
    # Fallback to PyPDF2
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        print(f"Successfully extracted text using PyPDF2 from {pdf_path}")
        return text
    except ImportError:
        print("PyPDF2 not available, trying pymupdf...")
    except Exception as e:
        print(f"Error with PyPDF2: {e}, trying pymupdf...")
    
    # Fallback to pymupdf (fitz)
    try:
        import fitz  # pymupdf
        doc = fitz.open(pdf_path)
        for page in doc:
            text += page.get_text() + "\n"
        doc.close()
        print(f"Successfully extracted text using pymupdf from {pdf_path}")
        return text
    except ImportError:
        print("pymupdf not available")
    except Exception as e:
        print(f"Error with pymupdf: {e}")
    
    raise Exception("No PDF extraction library available. Please install pdfplumber, PyPDF2, or pymupdf")

def is_russian_text(text):
    """Check if text contains significant Russian content"""
    # Count Cyrillic characters
    cyrillic_count = len(re.findall(r'[а-яё]', text.lower()))
    total_letters = len(re.findall(r'[a-zA-Zа-яёА-ЯЁ]', text))
    
    if total_letters == 0:
        return False
    
    # Consider text Russian if >70% of letters are Cyrillic
    return (cyrillic_count / total_letters) > 0.7

def clean_russian_text_for_tts(text):
    """Clean Russian text for optimal TTS processing"""
    
    # Remove page numbers, headers, footers (common patterns)
    text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)  # Standalone page numbers
    text = re.sub(r'^[IVX]+\.\s*$', '', text, flags=re.MULTILINE)  # Roman numerals
    text = re.sub(r'^\s*Глава\s+\d+\s*$', '', text, flags=re.MULTILINE)  # Chapter headers
    text = re.sub(r'^\s*ГЛАВА\s+[IVX\d]+\s*$', '', text, flags=re.MULTILINE)  # Chapter headers caps
    
    # Fix common OCR errors in Cyrillic text
    ocr_fixes = {
        # Common character substitutions
        'о': 'о',  # Latin 'o' to Cyrillic 'о'
        'а': 'а',  # Latin 'a' to Cyrillic 'а'
        'р': 'р',  # Latin 'p' to Cyrillic 'р'
        'е': 'е',  # Latin 'e' to Cyrillic 'е'
        'у': 'у',  # Latin 'y' to Cyrillic 'у'
        'х': 'х',  # Latin 'x' to Cyrillic 'х'
        'с': 'с',  # Latin 'c' to Cyrillic 'с'
        'В': 'В',  # Latin 'B' to Cyrillic 'В'
        'Н': 'Н',  # Latin 'H' to Cyrillic 'Н'
        'К': 'К',  # Latin 'K' to Cyrillic 'К'
        'М': 'М',  # Latin 'M' to Cyrillic 'М'
        'Р': 'Р',  # Latin 'P' to Cyrillic 'Р'
        'Т': 'Т',  # Latin 'T' to Cyrillic 'Т'
        'Х': 'Х',  # Latin 'X' to Cyrillic 'Х'
        'С': 'С',  # Latin 'C' to Cyrillic 'С'
        '0': 'о',  # Digit '0' to Cyrillic 'о' in word context
        '3': 'з',  # Digit '3' to Cyrillic 'з' in word context
        '6': 'б',  # Digit '6' to Cyrillic 'б' in word context
    }
    
    # Apply OCR fixes carefully (only in Cyrillic word contexts)
    for wrong, correct in ocr_fixes.items():
        if wrong != correct:  # Only apply if actually different
            # Fix in middle of Cyrillic words
            pattern = f'([а-яё]){re.escape(wrong)}([а-яё])'
            text = re.sub(pattern, f'\\1{correct}\\2', text, flags=re.IGNORECASE)
            
            # Fix at start of Cyrillic words
            pattern = f'^{re.escape(wrong)}([а-яё])'
            text = re.sub(pattern, f'{correct}\\1', text, flags=re.MULTILINE | re.IGNORECASE)
            
            # Fix at end of Cyrillic words
            pattern = f'([а-яё]){re.escape(wrong)}(?=\\s|$|[.,!?;:])'
            text = re.sub(pattern, f'\\1{correct}', text, flags=re.IGNORECASE)
    
    # Fix spacing issues
    text = re.sub(r'\s+', ' ', text)  # Multiple spaces to single space
    text = re.sub(r'([а-яё])([А-ЯЁ])', r'\1 \2', text)  # Missing space between words
    text = re.sub(r'([.!?])([А-ЯЁ])', r'\1 \2', text)  # Missing space after punctuation
    
    # Fix broken word boundaries
    text = re.sub(r'([а-яё])-\s*\n\s*([а-яё])', r'\1\2', text)  # Hyphenated words across lines
    text = re.sub(r'([а-яё])\s*\n\s*([а-яё])', r'\1\2', text)  # Words broken across lines
    
    # Normalize punctuation
    text = re.sub(r'\.{2,}', '...', text)  # Multiple dots to ellipsis
    text = re.sub(r'[""„"]', '"', text)  # Normalize quotes
    text = re.sub(r'[''`]', "'", text)  # Normalize apostrophes
    text = re.sub(r'—|–', '-', text)  # Normalize dashes
    
    # Add proper line breaks for better readability and TTS processing
    # Break at sentence endings
    text = re.sub(r'([.!?])\s+([А-ЯЁ])', r'\1\n\2', text)

    # Break at paragraph indicators
    text = re.sub(r'([.!?])\s*(ИЗ\s+ДНЕВНИКА|ИЗ\s+ПУТЕВЫХ\s+ЗАМЕТОК|ДОКТОРУ|АКАДЕМИКУ)', r'\1\n\n\2', text)
    text = re.sub(r'([.!?])\s*(\d{1,2}\s+[а-яё]+\s+\d{4}\s+г\.)', r'\1\n\n\2', text)  # Dates

    # Remove excessive line breaks but preserve paragraph structure
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple line breaks to double
    text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)  # Trim lines

    # Ensure proper sentence endings for TTS
    text = re.sub(r'([а-яё])(\s*\n\s*)([А-ЯЁ])', r'\1. \3', text)  # Add periods where missing
    
    return text.strip()

def filter_russian_content(text):
    """Extract only Russian language content from mixed-language text"""
    lines = text.split('\n')
    russian_lines = []
    
    for line in lines:
        line = line.strip()
        if line and is_russian_text(line):
            russian_lines.append(line)
    
    return '\n'.join(russian_lines)

def main():
    pdf_path = "text2.pdf"
    output_path = "text2_russian_extracted.txt"
    
    if not Path(pdf_path).exists():
        print(f"Error: PDF file '{pdf_path}' not found in current directory")
        return 1
    
    try:
        # Extract text from PDF
        print(f"Extracting text from {pdf_path}...")
        raw_text = extract_text_from_pdf(pdf_path)
        
        if not raw_text.strip():
            print("Error: No text could be extracted from the PDF")
            return 1
        
        print(f"Extracted {len(raw_text)} characters of raw text")
        
        # Filter for Russian content
        print("Filtering for Russian language content...")
        russian_text = filter_russian_content(raw_text)
        
        if not russian_text.strip():
            print("Warning: No Russian text detected in the PDF")
            print("Proceeding with all extracted text and applying Russian-specific cleaning...")
            russian_text = raw_text
        
        print(f"Found {len(russian_text)} characters of Russian text")
        
        # Clean text for TTS
        print("Cleaning text for TTS processing...")
        cleaned_text = clean_russian_text_for_tts(russian_text)
        
        print(f"Cleaned text: {len(cleaned_text)} characters")
        
        # Save to file with UTF-8 encoding and proper line breaks
        with open(output_path, 'w', encoding='utf-8') as f:
            # Split into sentences and write each on a new line for better TTS processing
            sentences = re.split(r'([.!?])\s+', cleaned_text)
            current_line = ""

            for i in range(0, len(sentences), 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i] + sentences[i + 1]
                else:
                    sentence = sentences[i]

                # Check if this looks like a section header or date
                if re.match(r'^(ИЗ\s+ДНЕВНИКА|ИЗ\s+ПУТЕВЫХ\s+ЗАМЕТОК|ДОКТОРУ|АКАДЕМИКУ|\d{1,2}\s+[а-яё]+\s+\d{4}\s+г\.)', sentence.strip()):
                    if current_line.strip():
                        f.write(current_line.strip() + '\n\n')
                        current_line = ""
                    f.write(sentence.strip() + '\n\n')
                else:
                    current_line += sentence + " "
                    # Write line if it gets too long (for readability)
                    if len(current_line) > 200:
                        f.write(current_line.strip() + '\n')
                        current_line = ""

            # Write any remaining text
            if current_line.strip():
                f.write(current_line.strip() + '\n')
        
        print(f"Successfully saved cleaned Russian text to '{output_path}'")
        print(f"Final text length: {len(cleaned_text)} characters")
        
        # Show preview
        preview = cleaned_text[:500] + "..." if len(cleaned_text) > 500 else cleaned_text
        print(f"\nPreview of extracted text:\n{'-'*50}")
        print(preview)
        print(f"{'-'*50}")
        
        return 0
        
    except Exception as e:
        print(f"Error processing PDF: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
