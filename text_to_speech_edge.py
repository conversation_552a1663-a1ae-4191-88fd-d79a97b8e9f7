import asyncio
import edge_tts
import re

# Settings
SOURCE_FILE = 'file.txt'
VOICE = "en-GB-RyanNeural" #ru-RU-SvetlanaNeural ru-RU-DmitryNeural fi-FI-HarriNeural fi-FI-NooraNeural en-GB-RyanNeural
WORDS_PER_MINUTE = 150
MAX_WORDS_PER_FILE = WORDS_PER_MINUTE * 45

# Read text from the file
def read_text_from_file(text_file) -> str:
    try:
        with open(text_file, 'r', encoding='utf-8') as file:
            print(f'{text_file} successfully opened.')
            return file.read()
    except Exception as e:
        print(f"Error opening '{text_file}': {e}")

def split_text_into_chunks(text, max_words=MAX_WORDS_PER_FILE) -> list[str]:
    """Split text into chunks based on word count, preserving sentence boundaries."""
    sentences = re.split(r'(?<=[.!?])\s+', text)
    chunks = []
    current_chunk = []
    current_word_count = 0
    
    for sentence in sentences:
        # Count words in this sentence
        sentence_words = len(sentence.split())
        
        # If adding this sentence would exceed the word limit
        if current_word_count + sentence_words > max_words:
            # Save current chunk if not empty
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            # Start new chunk with this sentence
            current_chunk = [sentence]
            current_word_count = sentence_words
        else:
            # Add sentence to current chunk
            current_chunk.append(sentence)
            current_word_count += sentence_words
    
    # Add the last chunk if not empty
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    
    return chunks

async def amain() -> None:
    text = read_text_from_file(SOURCE_FILE)
    text_chunks = split_text_into_chunks(text)
    
    # Generate audio file for each chunk
    for i, chunk_text in enumerate(text_chunks, 1):
        output_file = f"book_{i}.mp3"
        print(f"Generating file {output_file}...")
        communicate = edge_tts.Communicate(chunk_text, VOICE)
        await communicate.save(output_file)
        print(f"{output_file} successfully generated.")
        await asyncio.sleep(30)
    
    print("Completed!")

if __name__ == "__main__":
    loop = asyncio.get_event_loop_policy().get_event_loop()
    try:
        loop.run_until_complete(amain())
    finally:
        loop.close()